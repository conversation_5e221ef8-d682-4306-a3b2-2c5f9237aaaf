Stack trace:
Frame         Function      Args
0007FFFFB310  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB310, 0007FFFFA210) msys-2.0.dll+0x1FE8E
0007FFFFB310  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB5E8) msys-2.0.dll+0x67F9
0007FFFFB310  000210046832 (000210286019, 0007FFFFB1C8, 0007FFFFB310, 000000000000) msys-2.0.dll+0x6832
0007FFFFB310  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB310  000210068E24 (0007FFFFB320, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB5F0  00021006A225 (0007FFFFB320, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFC0AE0000 ntdll.dll
7FFFC0530000 KERNEL32.DLL
7FFFBD080000 KERNELBASE.dll
7FFFBE130000 USER32.dll
7FFFBCB30000 win32u.dll
7FFFC09D0000 GDI32.dll
7FFFBCBA0000 gdi32full.dll
7FFFBCD50000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFFBD320000 ucrtbase.dll
7FFFBEED0000 advapi32.dll
7FFFBEF80000 msvcrt.dll
7FFFC05F0000 sechost.dll
7FFFBDD30000 RPCRT4.dll
7FFFBCB70000 bcrypt.dll
7FFFBC470000 CRYPTBASE.DLL
7FFFBDC70000 bcryptPrimitives.dll
7FFFBDD00000 IMM32.DLL
