Stack trace:
Frame         Function      Args
0007FFFFBE80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBE80, 0007FFFFAD80) msys-2.0.dll+0x1FE8E
0007FFFFBE80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC158) msys-2.0.dll+0x67F9
0007FFFFBE80  000210046832 (000210286019, 0007FFFFBD38, 0007FFFFBE80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE80  000210068E24 (0007FFFFBE90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC160  00021006A225 (0007FFFFBE90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBAE090000 ntdll.dll
7FFBACFA0000 KERNEL32.DLL
7FFBAA0E0000 KERNELBASE.dll
7FFBAD200000 USER32.dll
000210040000 msys-2.0.dll
7FFBAAA00000 win32u.dll
7FFBAD1D0000 GDI32.dll
7FFBAA5B0000 gdi32full.dll
7FFBAA510000 msvcp_win.dll
7FFBAA410000 ucrtbase.dll
7FFBADA20000 advapi32.dll
7FFBAD3A0000 msvcrt.dll
7FFBAD730000 sechost.dll
7FFBACE20000 RPCRT4.dll
7FFBAA7B0000 bcrypt.dll
7FFBA9A20000 CRYPTBASE.DLL
7FFBAA380000 bcryptPrimitives.dll
7FFBACDF0000 IMM32.DLL
